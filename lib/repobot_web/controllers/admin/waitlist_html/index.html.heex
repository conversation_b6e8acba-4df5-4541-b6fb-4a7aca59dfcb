<div class="p-6">
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Waitlist</h1>
    <p class="mt-2 text-sm text-slate-600">Manage waitlist entries for your Repobot instance.</p>
  </div>

  <div class="overflow-hidden rounded-lg border border-slate-200">
    <table class="min-w-full divide-y divide-slate-200">
      <thead>
        <tr class="bg-slate-50">
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Email
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            GitHub Username
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Signed Up
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Used At
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Invitation
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-slate-200">
        <%= for entry <- @entries do %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
              {entry.email}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
              {entry.github_username}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
              {Calendar.strftime(entry.inserted_at, "%Y-%m-%d %H:%M")}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
              <%= if entry.used_at do %>
                {Calendar.strftime(entry.used_at, "%Y-%m-%d %H:%M")}
              <% else %>
                <span class="text-slate-400">Not used</span>
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <%= if entry.invitation_code do %>
                <div class="flex items-center gap-2">
                  <%= if url = Repobot.Waitlist.get_invite_url(entry) do %>
                    <a
                      href={url}
                      class="link link-primary flex items-center gap-2"
                      target="_blank"
                    >
                      <.icon name="hero-link" class="h-4 w-4" />
                      <span>
                        {URI.parse(url).host}/auth/github?invitation_code={entry.invitation_code}
                      </span>
                    </a>
                    <button
                      type="button"
                      class="text-slate-400 hover:text-slate-600"
                      phx-click={JS.dispatch("clipboard-copy")}
                      data-copy-target={"copy-target-#{entry.id}"}
                    >
                      <span class="sr-only">Copy URL</span>
                      <.icon name="hero-clipboard" class="h-4 w-4" />
                    </button>
                    <div id={"copy-target-#{entry.id}"} class="sr-only">{url}</div>
                  <% else %>
                    <span class="text-red-500 flex items-center gap-2">
                      <.icon name="hero-exclamation-circle" class="h-4 w-4" />
                      <span>URL host not configured</span>
                    </span>
                  <% end %>
                </div>
              <% else %>
                <span class="text-slate-500">Not generated</span>
              <% end %>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 flex items-center gap-2">
              <%= if !entry.invitation_code do %>
                <.form for={%{}} action={~p"/admin/waitlist/#{entry}/generate_code"} method="post">
                  <.button type="submit" class="link link-primary">
                    Generate Code
                  </.button>
                </.form>
              <% end %>
              <.form
                for={%{}}
                action={~p"/admin/waitlist/#{entry}"}
                method="delete"
                data-confirm="Are you sure you want to delete this entry?"
              >
                <.button type="submit">
                  Delete
                </.button>
              </.form>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
